mod config;
mod tun_device;
mod network;
mod socket;

use config::Config;
use network::{get_default_gateway, NetworkManager};
use socket::SocketManager;
use tun_device::{TunDevice, TunDeviceManager};

use anyhow::Result;
use log::{debug, error, info, warn};
use notify::{Event, EventKind, RecursiveMode, Watcher};
use std::fs;
use std::os::unix::fs::PermissionsExt;
use std::path::Path;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::sync::mpsc;
use tokio::sync::oneshot::{channel, Sender};
use tokio::time::{interval, Duration};
use tokio::{select, spawn};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    env_logger::init_from_env(env_logger::Env::default().default_filter_or("info"));
    info!("Starting flyshadow_mac_tun");

    // Create control file and setup file monitoring
    let control_file_path = "/tmp/flyshadow_control.lock";
    create_control_file(control_file_path)?;

    let (shutdown_tx, shutdown_rx) = channel::<u8>();

    // Setup file watcher
    spawn(async move {
        if let Err(e) = watch_control_file(control_file_path, shutdown_tx).await {
            error!("File watcher failed: {}", e);
        }
    });

    // Load configuration
    let config = Config::load()?;
    info!("Configuration loaded successfully");

    // Get default gateway
    let default_gateway = get_default_gateway()
        .ok_or_else(|| anyhow::anyhow!("No default gateway found"))?;
    info!("Default gateway: {}", default_gateway);

    // Create TUN device
    let tun_manager = TunDeviceManager::new(config.tun.clone());
    let device = tun_manager.create_device().await?;
    let tun_device = TunDevice::new(device);
    let (device_reader, device_writer) = tun_device.split();

    // Setup network routing
    let network_manager = NetworkManager::new(config.network.clone());
    network_manager.setup_routing(&default_gateway)?;

    // Connect to socket
    let socket_manager = SocketManager::new(config.socket.clone());
    let socket_connection = socket_manager.connect().await?;
    let (socket_reader, socket_writer) = socket_connection.split();


    select! {
        result = handle_device_to_socket(device_reader, socket_writer) => {
            if let Err(e) = result {
                error!("Socket to socket handler failed: {}", e);
            }
        }
        result = handle_socket_to_device(socket_reader, device_writer) => {
            if let Err(e) = result {
                error!("Socket to device handler failed: {}", e);
            }
        }
        _ = shutdown_rx => info!("Shutdown signal received")
    }

    // Clean up control file on exit
    if Path::new(control_file_path).exists() {
        if let Err(e) = fs::remove_file(control_file_path) {
            warn!("Failed to remove control file: {}", e);
        } else {
            info!("Control file removed on exit");
        }
    }

    Ok(())
}

/// Handle packets from TUN device to socket
async fn handle_device_to_socket(
    mut device_reader: impl AsyncReadExt + Unpin,
    mut socket_writer: socket::SocketWriter,
) -> Result<()> {
    let mut buf = vec![0u8; 8096]; // Use a reasonable buffer size

    loop {
        match device_reader.read(&mut buf).await {
            Ok(0) => {
                info!("Device reader reached EOF");
                break;
            }
            Ok(n) => {
                debug!("Read {} bytes from device", n);
                let packet = buf[..n].to_vec();

                // Send to socket
                if let Err(e) = socket_writer.write_packet(&packet).await {
                    error!("Failed to write packet to socket: {}", e);
                    break;
                }
            }
            Err(e) => {
                error!("Failed to read from device: {}", e);
                break;
            }
        }
    }

    Ok(())
}

/// Handle packets from socket to TUN device
async fn handle_socket_to_device(
    mut socket_reader: socket::SocketReader,
    mut device_writer: impl AsyncWriteExt + Unpin,
) -> Result<()> {
    loop {
        // Read packet from socket
        let packet = match socket_reader.read_packet().await {
            Ok(packet) => packet,
            Err(e) => {
                error!("Failed to read packet from socket: {}", e);
                break;
            }
        };

        debug!("Read {} bytes from socket", packet.len());

        // Write to device
        if let Err(e) = device_writer.write_all(&packet).await {
            error!("Failed to write packet to device: {}", e);
            break;
        }
    }

    Ok(())
}

/// Create control file
fn create_control_file(path: &str) -> Result<()> {
    let content = format!("flyshadow_mac_tun control file\nCreated at: {}\nPID: {}",
                          chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"),
                          std::process::id());

    fs::write(path, content)?;

    // Set permissions to 777 (read, write, execute for owner, group, and others)
    let mut perms = fs::metadata(path)?.permissions();
    perms.set_mode(0o777);
    fs::set_permissions(path, perms)?;

    info!("Control file created at: {} with permissions 777", path);
    Ok(())
}

/// Watch control file for shutdown content
async fn watch_control_file(path: &str, shutdown_flag: Sender<u8>) -> Result<()> {
    let (tx, mut rx) = mpsc::channel(100);

    let mut watcher = notify::recommended_watcher(move |res: Result<Event, notify::Error>| {
        match res {
            Ok(event) => {
                if let Err(e) = tx.blocking_send(event) {
                    error!("Failed to send file event: {}", e);
                }
            }
            Err(e) => error!("File watch error: {}", e),
        }
    })?;

    let watch_path = Path::new(path).canonicalize()?;
    let parent_dir = watch_path.parent().unwrap_or_else(|| Path::new("."));

    watcher.watch(parent_dir, RecursiveMode::NonRecursive)?;
    info!("Started watching control file for shutdown content: {} (canonical: {:?})", path, watch_path);

    while let Some(event) = rx.recv().await {
        debug!("File event received: {:?}", event);

        match event.kind {
            EventKind::Modify(_) | EventKind::Create(_) | EventKind::Any => {
                for event_path in &event.paths {
                    debug!("Checking event path: {:?} against watch path: {:?}", event_path, watch_path);
                    if event_path == &watch_path {
                        debug!("Event matches watch path, checking file content");
                        // Check if file contains "shutdown" string
                        if let Ok(content) = fs::read_to_string(&watch_path) {
                            debug!("File content: {:?}", content);
                            if content.contains("shutdown") {
                                warn!("Shutdown signal detected in control file: {:?}", event_path);
                                info!("Initiating shutdown due to shutdown signal in control file");
                                let _ = shutdown_flag.send(0);
                                return Ok(());
                            }
                        } else {
                            warn!("Failed to read control file content: {:?}", event_path);
                        }
                    }
                }
            }
            _ => {
                // Ignore other events
            }
        }
    }

    Ok(())
}

