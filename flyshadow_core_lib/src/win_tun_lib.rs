use flyshadow_common::interface::interface_selector::{IfaceSelectType, InterfaceSelector};
use log::{error, info, Level};
use std::ffi::{CStr, CString};
use std::mem::forget;
use std::net::IpAddr;
use std::os::raw::c_char;
use std::sync::{Arc, Mutex};
use std::thread::spawn;
use std::time::Duration;
use tokio::io::AsyncWriteExt;
use tokio::runtime::Runtime;
use tokio::task::Join<PERSON>andle;
use tokio::time::sleep;
use trust_dns_resolver::config::{ResolverConfig, ResolverOpts};
use trust_dns_resolver::Resolver;
use winroute::{Route, RouteManager};
use wintun::{Adapter, Error, Packet, Session, Wintun};

use tunnel::context::context::TunnelContext;
use tunnel::tun::tun::Tun;

// 常量定义
const TUN_ADAPTER_NAME: &str = "FlyShadow";
const TUN_TUNNEL_TYPE: &str = "FlyShadowTun";
const TUN_ADAPTER_GUID: Option<u128> = Some(2596136378334892578531340212764676);
const TUN_IP_ADDRESS: &str = "***********";
const TUN_SUBNET_MASK: &str = "*************";
const TUN_DNS_SERVERS: [&str; 2] = ["*******", "*******"];
const TUN_ROUTE_NETWORK: &str = "**********";
const TUN_ROUTE_PREFIX: u8 = 19;
const INTERFACE_CHECK_INTERVAL: u64 = 30; // 秒
const MAX_RETRY_ATTEMPTS: u8 = 2;

// 局域网网段定义
const LOCAL_NETWORKS: &[(&str, u8)] = &[
    ("10.0.0.0", 8),
    ("***********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
    ("**********", 16),
];

/// Windows TUN 环境管理器
/// 负责管理 WinTun 适配器、路由表、数据传输线程等
pub struct WinTunEnv {
    /// Tokio 异步运行时
    runtime: Runtime,
    /// 路由管理器，用于添加和删除系统路由
    route_manager: Arc<RouteManager>,
    /// WinTun 驱动实例
    wintun: Wintun,
    /// TUN 适配器实例，用于网络接口操作
    adapter: Option<Arc<Adapter>>,
    /// TUN 会话指针，用于数据包收发
    tun_session: Option<i64>,
    /// TUN 数据发送线程句柄（从 TUN 到网络栈）
    tun_send_thread: Arc<Mutex<Option<JoinHandle<()>>>>,
    /// 网络接口选择检查线程句柄
    interface_check_thread: Arc<Mutex<Option<JoinHandle<()>>>>,
    /// TUN 数据接收线程句柄（从网络栈到 TUN）
    tun_receive_thread: Arc<Mutex<Option<std::thread::JoinHandle<()>>>>,
    /// 已添加的路由列表，用于清理时删除
    routes: Vec<Route>,
    /// TUN 接口实例，处理网络数据包
    tun: Arc<Tun>,
    /// 隧道上下文，包含代理配置和连接信息
    tunnel_context: Arc<TunnelContext>,
}

impl WinTunEnv {
    /// 创建新的 WinTun 环境
    ///
    /// # 参数
    /// * `tc` - 隧道上下文，包含代理配置和连接信息
    /// * `win_tun_lib_path` - WinTun 动态库文件路径
    ///
    /// # 返回值
    /// 返回初始化完成的 WinTunEnv 实例
    pub fn new(tc: Arc<TunnelContext>, win_tun_lib_path: String) -> Result<WinTunEnv, String> {
        // 加载 WinTun 驱动库
        let wintun = unsafe {
            wintun::load_from_path(win_tun_lib_path.clone())
                .map_err(|e| format!("加载 WinTun 驱动失败: {}", e))?
        };

        // 创建路由管理器
        let route_manager = Arc::new(
            RouteManager::new()
                .map_err(|e| format!("创建路由管理器失败: {}", e))?
        );

        let tunnel_context = tc.clone();

        // 创建多线程 Tokio 运行时
        let runtime = tokio::runtime::Builder::new_multi_thread()
            .enable_all()
            .build()
            .map_err(|e| format!("创建异步运行时失败: {}", e))?;

        // 初始化 TUN 接口
        let tun = runtime.block_on(async move {
            Arc::new(Tun::new(tc).await.unwrap())
        });

        Ok(WinTunEnv {
            runtime,
            route_manager,
            wintun,
            adapter: None,
            tun_session: None,
            tun_send_thread: Arc::new(Mutex::new(None)),
            interface_check_thread: Arc::new(Mutex::new(None)),
            tun_receive_thread: Arc::new(Mutex::new(None)),
            routes: Vec::new(),
            tun,
            tunnel_context,
        })
    }

    /// 打开并配置 TUN 网络适配器
    ///
    /// # 返回值
    /// * `Ok(())` - 成功创建并配置适配器
    /// * `Err(String)` - 失败时返回错误信息
    pub fn open_tun(&mut self) -> Result<(), String> {
        // 尝试创建或打开 TUN 网络适配器
        let adapter = self.create_or_open_adapter()?;

        // 配置网络地址和 DNS
        self.configure_adapter(&adapter)?;

        // 启动网络会话
        let session = self.start_adapter_session(&adapter)?;
        self.tun_session = Some(Box::into_raw(Box::new(Arc::new(session))) as i64);

        // 配置路由表
        self.setup_routes(&adapter)?;

        // 初始化网络接口选择
        self.initialize_interface_selection()?;

        // 启动定时检查网络接口的任务
        self.start_interface_monitoring();

        self.adapter = Some(adapter);
        Ok(())
    }

    /// 创建或打开 TUN 适配器
    fn create_or_open_adapter(&self) -> Result<Arc<Adapter>, String> {
        let mut retry_count = MAX_RETRY_ATTEMPTS;

        loop {
            // 首先尝试打开已存在的适配器
            match Adapter::open(&self.wintun, TUN_ADAPTER_NAME) {
                Ok(adapter) => return Ok(adapter),
                Err(_) => {
                    // 如果打开失败，尝试创建新的适配器
                    match Adapter::create(&self.wintun, TUN_ADAPTER_NAME, TUN_TUNNEL_TYPE, TUN_ADAPTER_GUID) {
                        Ok(adapter) => return Ok(adapter),
                        Err(e) => {
                            if retry_count == 0 {
                                return Err(format!("创建 TUN 适配器失败: {}", e));
                            }
                            retry_count -= 1;
                            info!("创建适配器失败，剩余重试次数: {}", retry_count);
                        }
                    }
                }
            }
        }
    }

    /// 配置适配器的网络地址和 DNS
    fn configure_adapter(&self, adapter: &Arc<Adapter>) -> Result<(), String> {
        // 设置网卡 IP 地址和子网掩码
        let ip_addr = TUN_IP_ADDRESS.parse()
            .map_err(|e| format!("解析 IP 地址失败: {}", e))?;
        let subnet_mask = TUN_SUBNET_MASK.parse()
            .map_err(|e| format!("解析子网掩码失败: {}", e))?;

        adapter.set_network_addresses_tuple(ip_addr, subnet_mask, None)
            .map_err(|e| format!("设置网络地址失败: {}", e))?;

        // 设置 DNS 服务器
        let dns_servers: Result<Vec<_>, _> = TUN_DNS_SERVERS
            .iter()
            .map(|dns| dns.parse())
            .collect();
        let dns_servers = dns_servers
            .map_err(|e| format!("解析 DNS 服务器地址失败: {}", e))?;

        adapter.set_dns_servers(&dns_servers)
            .map_err(|e| format!("设置 DNS 服务器失败: {}", e))?;

        Ok(())
    }

    /// 启动适配器会话
    fn start_adapter_session(&self, adapter: &Arc<Adapter>) -> Result<Session, String> {
        adapter.start_session(wintun::MAX_RING_CAPACITY)
            .map_err(|e| format!("启动网络会话失败: {}", e))
    }
    /// 设置路由表
    fn setup_routes(&mut self, adapter: &Arc<Adapter>) -> Result<(), String> {
        let adapter_index = adapter.get_adapter_index()
            .map_err(|e| format!("获取适配器索引失败: {}", e))?;

        // 添加默认路由（所有流量通过 TUN）
        let default_route = Route::new("0.0.0.0".parse().unwrap(), 0)
            .metric(1)
            .ifindex(adapter_index);
        if let Err(e) = self.route_manager.add_route(&default_route) {
            error!("添加默认路由失败: {}", e);
        }

        // 添加 TUN 网络路由
        let tun_route = Route::new(TUN_ROUTE_NETWORK.parse().unwrap(), TUN_ROUTE_PREFIX)
            .metric(1)
            .ifindex(adapter_index);
        if let Err(e) = self.route_manager.add_route(&tun_route) {
            error!("添加 TUN 网络路由失败: {}", e);
        }

        // 添加局域网路由（保持局域网流量走原网卡）
        self.setup_local_network_routes()?;

        Ok(())
    }

    /// 设置局域网路由，确保局域网流量不走 TUN
    fn setup_local_network_routes(&mut self) -> Result<(), String> {
        if let Ok(Some(mut default_route)) = self.route_manager.default_route() {
            // 为所有局域网网段添加路由
            for &(network, prefix) in LOCAL_NETWORKS {
                self.add_local_network_route(&mut default_route, network, prefix);
            }
        } else {
            error!("无法获取默认路由，跳过局域网路由设置");
        }
        Ok(())
    }

    /// 初始化网络接口选择
    fn initialize_interface_selection(&self) -> Result<(), String> {
        let tunnel_context = self.tunnel_context.clone();
        self.runtime.block_on(async move {
            let interface_selector = InterfaceSelector::select_iface(
                IfaceSelectType::IP,
                tunnel_context.get_ipv6_enable().await,
            ).await
                .map_err(|e| format!("选择网络接口失败: {}", e))?;

            tunnel_context.set_local_interface(interface_selector).await;
            Ok(())
        })
    }

    /// 启动网络接口监控任务
    fn start_interface_monitoring(&self) {
        let tunnel_context = self.tunnel_context.clone();
        let monitoring_task = self.runtime.spawn(async move {
            loop {
                sleep(Duration::from_secs(INTERFACE_CHECK_INTERVAL)).await;

                // 定期检查并更新网络接口
                match InterfaceSelector::select_iface(
                    IfaceSelectType::IP,
                    tunnel_context.get_ipv6_enable().await,
                ).await {
                    Ok(interface_selector) => {
                        tunnel_context.set_local_interface(interface_selector).await;
                    }
                    Err(e) => {
                        error!("定期检查网络接口失败: {}", e);
                    }
                }
            }
        });

        if let Ok(mut thread_guard) = self.interface_check_thread.lock() {
            *thread_guard = Some(monitoring_task);
        }
    }

    /// 添加局域网路由
    ///
    /// # 参数
    /// * `route` - 基础路由模板
    /// * `destination` - 目标网络地址
    /// * `prefix` - 网络前缀长度
    fn add_local_network_route(&mut self, route: &mut Route, destination: &str, prefix: u8) {
        if let Ok(dest_addr) = destination.parse() {
            route.destination = dest_addr;
            route.prefix = prefix;
            route.metric = Some(1);

            match self.route_manager.add_route(route) {
                Ok(_) => {
                    self.routes.push(route.clone());
                    info!("成功添加局域网路由: {}/{}", destination, prefix);
                }
                Err(e) => {
                    error!("添加局域网路由失败 {}/{}: {}", destination, prefix, e);
                }
            }
        } else {
            error!("无效的网络地址: {}", destination);
        }
    }

    /// 添加自定义路由
    ///
    /// # 参数
    /// * `dest_ip` - 目标 IP 地址
    /// * `prefix` - 网络前缀长度
    /// * `gateway` - 网关地址
    /// * `if_index` - 网络接口索引
    ///
    /// # 返回值
    /// * 成功时返回空字符串
    /// * 失败时返回错误信息
    pub fn add_route(&mut self, dest_ip: String, prefix: u8, gateway: String, if_index: u32) -> Result<(), String> {
        let dest_ip: IpAddr = dest_ip.parse()
            .map_err(|e| format!("解析目标 IP 地址失败: {}", e))?;

        let gateway: IpAddr = gateway.parse()
            .map_err(|e| format!("解析网关地址失败: {}", e))?;

        let route = Route::new(dest_ip, prefix)
            .metric(1)
            .ifindex(if_index)
            .gateway(gateway);

        self.route_manager.add_route(&route)
            .map_err(|e| format!("添加路由失败: {}", e))?;

        self.routes.push(route);
        info!("成功添加路由: {} -> {} (接口: {})", dest_ip, gateway, if_index);
        Ok(())
    }

    /// 启动 TUN 数据交换
    ///
    /// 启动两个线程：
    /// 1. 从 TUN 接口读取数据并发送到网络栈
    /// 2. 从网络栈接收数据并写入 TUN 接口
    ///
    /// # 返回值
    /// * `Ok(())` - 成功启动数据交换
    /// * `Err(String)` - 启动失败时返回错误信息
    pub fn start_tun_exchange(&mut self) -> Result<(), String> {
        let session_ptr = self.tun_session
            .ok_or("TUN 会话未初始化")?;

        // 安全地获取会话引用
        let session_box = unsafe { Box::from_raw(session_ptr as *mut Arc<Session>) };
        let session = session_box.as_ref().clone();
        let session_for_receive = session.clone();

        // 启动发送线程（TUN -> 网络栈）
        self.start_send_thread(session.clone())?;

        // 启动接收线程（网络栈 -> TUN）
        self.start_receive_thread(session_for_receive)?;

        // 防止 session_box 被释放
        forget(session_box);

        info!("TUN 数据交换已启动");
        Ok(())
    }

    /// 启动发送线程（从 TUN 接口发送数据到网络栈）
    fn start_send_thread(&self, session: Arc<Session>) -> Result<(), String> {
        let tun = self.tun.clone();
        let send_task = self.runtime.spawn(async move {
            info!("TUN 发送线程已启动");
            loop {
                // 从 TUN 接口获取数据
                let data = tun.get_tun_data().await;

                // 分配发送数据包并发送
                match session.allocate_send_packet(data.len() as u16) {
                    Ok(mut packet) => {
                        packet.bytes_mut().copy_from_slice(&data);
                        session.send_packet(packet);
                    }
                    Err(e) => {
                        error!("分配发送数据包失败: {}", e);
                        break;
                    }
                }
            }
            info!("TUN 发送线程已退出");
        });

        if let Ok(mut thread_guard) = self.tun_send_thread.lock() {
            *thread_guard = Some(send_task);
        }

        Ok(())
    }

    /// 启动接收线程（从网络栈接收数据到 TUN 接口）
    fn start_receive_thread(&self, session: Arc<Session>) -> Result<(), String> {
        let tun = self.tun.clone();
        let receive_task = spawn(move || {
            // 创建单线程运行时用于异步操作
            let rt = tokio::runtime::Builder::new_current_thread()
                .enable_all()
                .build()
                .expect("创建接收线程运行时失败");

            rt.block_on(async move {
                info!("TUN 接收线程已启动");
                loop {
                    match session.receive_blocking() {
                        Ok(packet) => {
                            let bytes = packet.bytes();
                            if bytes.is_empty() {
                                info!("接收到空数据包，退出接收循环");
                                break;
                            }
                            // 将数据传递给 TUN 处理器
                            tun.handler_tun_data(bytes.to_vec()).await;
                        }
                        Err(e) => {
                            error!("接收数据包失败: {}", e);
                            break;
                        }
                    }
                }
                info!("TUN 接收线程已退出");
            });
        });

        if let Ok(mut thread_guard) = self.tun_receive_thread.lock() {
            *thread_guard = Some(receive_task);
        }

        Ok(())
    }

    /// 停止 TUN 环境并清理资源
    ///
    /// 按顺序执行以下清理操作：
    /// 1. 停止所有工作线程
    /// 2. 关闭 TUN 会话
    /// 3. 释放网络适配器
    /// 4. 删除添加的路由
    pub fn stop(&mut self) {
        info!("开始停止 TUN 环境");

        // 停止网络接口检查线程
        self.stop_interface_monitoring();

        // 停止数据交换线程
        self.stop_data_exchange_threads();

        // 关闭 TUN 会话
        self.shutdown_tun_session();

        // 释放网络适配器
        self.release_adapter();

        // 清理路由表
        self.cleanup_routes();

        info!("TUN 环境已完全停止");
    }

    /// 停止网络接口监控线程
    fn stop_interface_monitoring(&self) {
        if let Ok(mut thread_guard) = self.interface_check_thread.lock() {
            if let Some(thread) = thread_guard.take() {
                thread.abort();
                info!("网络接口监控线程已停止");
            }
        }
    }

    /// 停止数据交换线程
    fn stop_data_exchange_threads(&self) {
        // 停止发送线程
        if let Ok(mut thread_guard) = self.tun_send_thread.lock() {
            if let Some(thread) = thread_guard.take() {
                thread.abort();
                info!("TUN 发送线程已停止");
            }
        }

        // 停止接收线程（注意：std::thread::JoinHandle 无法 abort，只能等待自然结束）
        if let Ok(mut thread_guard) = self.tun_receive_thread.lock() {
            if let Some(_thread) = thread_guard.take() {
                info!("TUN 接收线程将自然结束");
            }
        }
    }

    /// 关闭 TUN 会话
    fn shutdown_tun_session(&mut self) {
        if let Some(session_ptr) = self.tun_session.take() {
            info!("正在关闭 TUN 会话");
            let session_box = unsafe { Box::from_raw(session_ptr as *mut Arc<Session>) };
            if let Err(e) = session_box.shutdown() {
                error!("关闭 TUN 会话失败: {}", e);
            } else {
                info!("TUN 会话已关闭");
            }
        }
    }

    /// 释放网络适配器
    fn release_adapter(&mut self) {
        if let Some(_adapter) = self.adapter.take() {
            info!("网络适配器已释放");
        }
    }

    /// 清理添加的路由
    fn cleanup_routes(&mut self) {
        let route_count = self.routes.len();
        for route in self.routes.drain(..) {
            if let Err(e) = self.route_manager.delete_route(&route) {
                error!("删除路由失败: {} -> {}", route.destination, e);
            }
        }
        info!("已清理 {} 条路由", route_count);
    }
}

/// 创建新的 Windows TUN 环境
///
/// # 参数
/// * `context_ptr` - 隧道上下文指针
/// * `win_tun_lib_path` - WinTun 动态库路径
///
/// # 返回值
/// * 成功时返回 WinTunEnv 实例指针
/// * 失败时返回 0
///
/// # 安全性
/// 此函数使用 unsafe 代码处理 C 接口，调用者需要确保传入的指针有效
#[unsafe(no_mangle)]
pub extern "C" fn new_win_tun_env(context_ptr: i64, win_tun_lib_path: *const c_char) -> i64 {
    // 安全地从指针恢复 TunnelContext
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };

    // 转换 C 字符串为 Rust 字符串
    let win_tun_lib_path = unsafe {
        CStr::from_ptr(win_tun_lib_path).to_string_lossy().to_string()
    };

    // 创建 WinTun 环境
    match WinTunEnv::new(tc.as_ref().clone(), win_tun_lib_path) {
        Ok(win_tun_env) => {
            forget(tc); // 防止 tc 被释放
            Box::into_raw(Box::new(win_tun_env)) as i64
        }
        Err(e) => {
            error!("创建 WinTun 环境失败: {}", e);
            forget(tc); // 防止 tc 被释放
            0 // 返回空指针表示失败
        }
    }
}

/// 打开并配置 Windows TUN 网卡
///
/// # 参数
/// * `win_tun_env_ptr` - WinTunEnv 实例指针
///
/// # 返回值
/// * 成功时返回空字符串的 C 字符串指针
/// * 失败时返回错误信息的 C 字符串指针
///
/// # 安全性
/// 调用者需要确保传入的指针有效，并负责释放返回的字符串内存
#[unsafe(no_mangle)]
pub extern "C" fn open_win_tun(win_tun_env_ptr: i64) -> *mut c_char {
    if win_tun_env_ptr == 0 {
        return CString::new("无效的 WinTunEnv 指针").unwrap().into_raw();
    }

    let mut win_tun_env = unsafe { Box::from_raw(win_tun_env_ptr as *mut WinTunEnv) };

    let result = match win_tun_env.open_tun() {
        Ok(_) => {
            info!("TUN 网卡已成功打开");
            String::new()
        }
        Err(e) => {
            error!("打开 TUN 网卡失败: {}", e);
            e
        }
    };

    forget(win_tun_env); // 防止环境被释放
    CString::new(result).unwrap().into_raw()
}

/// 停止 Windows TUN 环境
///
/// # 参数
/// * `win_tun_env_ptr` - WinTunEnv 实例指针
///
/// # 安全性
/// 调用者需要确保传入的指针有效
#[unsafe(no_mangle)]
pub extern "C" fn stop_win_tun(win_tun_env_ptr: i64) {
    if win_tun_env_ptr == 0 {
        error!("尝试停止无效的 WinTunEnv 指针");
        return;
    }

    let mut win_tun_env = unsafe { Box::from_raw(win_tun_env_ptr as *mut WinTunEnv) };
    win_tun_env.stop();
    forget(win_tun_env); // 防止环境被释放
    info!("WinTun 已停止");
}

/// 启动 TUN 数据交换
///
/// # 参数
/// * `win_tun_env_ptr` - WinTunEnv 实例指针
///
/// # 返回值
/// * 成功时返回空字符串的 C 字符串指针
/// * 失败时返回错误信息的 C 字符串指针
///
/// # 安全性
/// 调用者需要确保传入的指针有效，并负责释放返回的字符串内存
#[unsafe(no_mangle)]
pub extern "C" fn start_win_tun_exchange(win_tun_env_ptr: i64) -> *mut c_char {
    if win_tun_env_ptr == 0 {
        return CString::new("无效的 WinTunEnv 指针").unwrap().into_raw();
    }

    let mut win_tun_env = unsafe { Box::from_raw(win_tun_env_ptr as *mut WinTunEnv) };

    let result = match win_tun_env.start_tun_exchange() {
        Ok(_) => {
            info!("TUN 数据交换已启动");
            String::new()
        }
        Err(e) => {
            error!("启动 TUN 数据交换失败: {}", e);
            e
        }
    };

    forget(win_tun_env); // 防止环境被释放
    CString::new(result).unwrap().into_raw()
}