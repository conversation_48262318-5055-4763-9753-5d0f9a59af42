//! # TUN 模块单元测试
//!
//! 本模块包含 TUN 设备各个组件的单元测试，
//! 确保重构后的代码功能正确性和稳定性。

#[cfg(test)]
mod tests {
    use super::super::*;
    use std::sync::Arc;
    use std::time::Duration;
    use tokio::time::timeout;

    /// 测试 TUN 配置的创建和验证
    #[test]
    fn test_tun_config_creation() {
        let config = TunConfig::default();
        assert_eq!(config.netstack_tcp_buffer_size, config::NETSTACK_TCP_BUFFER_SIZE);
        assert_eq!(config.netstack_udp_buffer_size, config::NETSTACK_UDP_BUFFER_SIZE);
        assert!(config.enable_dns_blocking);
    }

    #[test]
    fn test_tun_config_builder() {
        let config = TunConfig::new()
            .with_channel_buffer_size(200)
            .with_netstack_buffer_size(1024, 512)
            .with_dns_blocking(false);

        assert_eq!(config.channel_buffer_size, 200);
        assert_eq!(config.netstack_tcp_buffer_size, 1024);
        assert_eq!(config.netstack_udp_buffer_size, 512);
        assert!(!config.enable_dns_blocking);
    }

    #[test]
    fn test_dns_blocking() {
        let config = TunConfig::default();

        // 测试默认封锁地址
        assert!(config.is_dns_address_blocked("*******:853"));
        assert!(config.is_dns_address_blocked("*******:443"));
        assert!(!config.is_dns_address_blocked("*******:80"));

        // 测试自定义封锁地址
        let config = config.with_custom_blocked_dns_addresses(vec!["custom.dns:853".to_string()]);
        assert!(config.is_dns_address_blocked("custom.dns:853"));
        assert!(!config.is_dns_address_blocked("other.dns:853"));

        // 测试禁用 DNS 封锁
        let config = config.with_dns_blocking(false);
        assert!(!config.is_dns_address_blocked("*******:853"));
        assert!(!config.is_dns_address_blocked("custom.dns:853"));
    }

    /// 测试错误类型和恢复策略
    #[test]
    fn test_error_types() {
        let error = TunError::NetStackInitError("test error".to_string());
        assert_eq!(format!("{}", error), "网络栈初始化错误: test error");
        assert!(error.is_fatal());
        assert!(!error.is_retryable());

        let error = TunError::ChannelError("test error".to_string());
        assert!(!error.is_fatal());
        assert!(error.is_retryable());
        assert!(error.retry_delay_ms() > 0);
    }

    #[test]
    fn test_error_conversion() {
        use std::io;

        let io_error = io::Error::new(io::ErrorKind::ConnectionRefused, "connection refused");
        let tun_error: TunError = io_error.into();

        match tun_error {
            TunError::IoError(_) => (),
            _ => panic!("Expected IoError"),
        }
    }

    /// 测试网络栈管理器
    #[test]
    fn test_netstack_manager() {
        let config = TunConfig::default();
        let manager = NetStackManager::new(config.clone());

        assert_eq!(manager.config().netstack_tcp_buffer_size, config.netstack_tcp_buffer_size);
        assert_eq!(manager.config().netstack_udp_buffer_size, config.netstack_udp_buffer_size);
        assert!(manager.validate_config().is_ok());

        let stats = manager.get_stats();
        assert_eq!(stats.tcp_buffer_size, config.netstack_tcp_buffer_size);
        assert_eq!(stats.udp_buffer_size, config.netstack_udp_buffer_size);
    }

    #[test]
    fn test_netstack_manager_invalid_config() {
        let mut config = TunConfig::default();
        config.netstack_tcp_buffer_size = 0;

        let manager = NetStackManager::new(config);
        assert!(manager.validate_config().is_err());
    }

    /// 测试性能监控指标
    #[tokio::test]
    async fn test_metrics() {
        let metrics = TunMetrics::new();

        // 测试 TCP 指标
        metrics.record_tcp_connection_start();
        assert_eq!(metrics.tcp_stats.total_connections.load(std::sync::atomic::Ordering::Relaxed), 1);
        assert_eq!(metrics.tcp_stats.active_connections.load(std::sync::atomic::Ordering::Relaxed), 1);

        metrics.record_tcp_connection_end(true, Duration::from_millis(100));
        assert_eq!(metrics.tcp_stats.active_connections.load(std::sync::atomic::Ordering::Relaxed), 0);
        assert_eq!(metrics.tcp_stats.successful_connections.load(std::sync::atomic::Ordering::Relaxed), 1);

        // 测试 UDP 指标
        metrics.record_udp_packet(1024, true, false);
        assert_eq!(metrics.udp_stats.total_packets.load(std::sync::atomic::Ordering::Relaxed), 1);
        assert_eq!(metrics.udp_stats.processed_packets.load(std::sync::atomic::Ordering::Relaxed), 1);
        assert_eq!(metrics.udp_stats.total_bytes.load(std::sync::atomic::Ordering::Relaxed), 1024);

        // 测试错误记录
        metrics.record_error("tcp_connection");
        assert_eq!(metrics.error_stats.tcp_connection_errors.load(std::sync::atomic::Ordering::Relaxed), 1);
    }

    #[test]
    fn test_metrics_summary() {
        let metrics = TunMetrics::new();
        metrics.record_tcp_connection_start();
        metrics.record_udp_packet(1024, true, false);

        let summary = metrics.get_summary();
        assert!(summary.contains("TCP连接"));
        assert!(summary.contains("UDP数据包"));
        assert!(summary.contains("流量"));
    }

    /// 测试隧道映射信息创建
    #[test]
    fn test_tunnel_mapper_info_creation() {
        // 这里需要模拟 TunnelContext，在实际测试中需要创建 mock
        // 由于 TunnelContext 的复杂性，这里只测试基本的结构创建

        // 测试 TCP 隧道映射信息的基本字段
        // 在实际实现中，需要通过 TcpHandler 的公共方法来测试

        // 测试 UDP 隧道映射信息的基本字段
        // 在实际实现中，需要通过 UdpHandler 的公共方法来测试
    }

    /// 集成测试 - 测试组件之间的协作
    #[tokio::test]
    async fn test_component_integration() {
        // 这是一个集成测试的框架
        // 在实际实现中，需要创建 mock 的 TunnelContext
        // 然后测试各个组件之间的协作

        let config = TunConfig::default()
            .with_channel_buffer_size(10)
            .with_netstack_buffer_size(256, 128);

        // 验证配置
        let manager = NetStackManager::new(config.clone());
        assert!(manager.validate_config().is_ok());

        // 测试指标收集
        let metrics = TunMetrics::new();

        // 模拟一些活动
        metrics.record_tcp_connection_start();
        metrics.record_udp_packet(512, true, false);

        // 验证指标
        assert_eq!(metrics.tcp_stats.total_connections.load(std::sync::atomic::Ordering::Relaxed), 1);
        assert_eq!(metrics.udp_stats.total_packets.load(std::sync::atomic::Ordering::Relaxed), 1);
    }

    /// 性能测试 - 测试在高负载下的表现
    #[tokio::test]
    async fn test_performance_under_load() {
        let metrics = TunMetrics::new();

        // 模拟大量连接
        for _ in 0..1000 {
            metrics.record_tcp_connection_start();
            metrics.record_tcp_connection_end(true, Duration::from_millis(10));
        }

        // 模拟大量 UDP 数据包
        for i in 0..10000 {
            metrics.record_udp_packet(1024 + i % 1000, true, false);
        }

        // 验证统计信息
        assert_eq!(metrics.tcp_stats.total_connections.load(std::sync::atomic::Ordering::Relaxed), 1000);
        assert_eq!(metrics.udp_stats.total_packets.load(std::sync::atomic::Ordering::Relaxed), 10000);

        // 验证平均值计算
        let avg_packet_size = metrics.udp_stats.avg_packet_size.load(std::sync::atomic::Ordering::Relaxed);
        assert!(avg_packet_size > 1000); // 应该大于基础大小
    }

    /// 错误处理测试
    #[tokio::test]
    async fn test_error_handling() {
        use crate::tun::error::error_utils;

        let error = TunError::ChannelError("test error".to_string());

        // 测试错误恢复策略
        let should_continue = error_utils::handle_error_recovery(&error, "test_context").await;
        assert!(should_continue); // 通道错误应该可以恢复

        let fatal_error = TunError::NetStackInitError("fatal error".to_string());
        let should_continue = error_utils::handle_error_recovery(&fatal_error, "test_context").await;
        assert!(!should_continue); // 网络栈初始化错误是致命的
    }

    /// 并发安全测试
    #[tokio::test]
    async fn test_concurrent_access() {
        let metrics = Arc::new(TunMetrics::new());
        let mut handles = vec![];

        // 启动多个任务并发访问指标
        for i in 0..10 {
            let metrics_clone = metrics.clone();
            let handle = tokio::spawn(async move {
                for j in 0..100 {
                    metrics_clone.record_tcp_connection_start();
                    metrics_clone.record_udp_packet(1024 + j, true, false);
                    metrics_clone.record_tcp_connection_end(true, Duration::from_millis(i * 10));
                }
            });
            handles.push(handle);
        }

        // 等待所有任务完成
        for handle in handles {
            handle.await.unwrap();
        }

        // 验证最终结果
        assert_eq!(metrics.tcp_stats.total_connections.load(std::sync::atomic::Ordering::Relaxed), 1000);
        assert_eq!(metrics.udp_stats.total_packets.load(std::sync::atomic::Ordering::Relaxed), 1000);
    }

    /// 超时测试
    #[tokio::test]
    async fn test_timeout_handling() {
        // 测试各种操作的超时处理
        let config = TunConfig::default();

        // 测试配置验证不会超时
        let result = timeout(Duration::from_secs(1), async {
            let manager = NetStackManager::new(config);
            manager.validate_config()
        }).await;

        assert!(result.is_ok());
        assert!(result.unwrap().is_ok());
    }
}

/// 基准测试模块
#[cfg(test)]
mod benchmarks {
    use super::super::*;
    use std::time::Instant;

    /// 基准测试 - 指标记录性能
    #[tokio::test]
    async fn bench_metrics_recording() {
        let metrics = TunMetrics::new();
        let start = Instant::now();

        // 记录大量指标
        for i in 0..100000 {
            metrics.record_tcp_connection_start();
            metrics.record_udp_packet(1024 + i % 1000, true, false);
            if i % 2 == 0 {
                metrics.record_tcp_connection_end(true, std::time::Duration::from_millis(10));
            }
        }

        let duration = start.elapsed();
        println!("记录 100,000 个指标耗时: {:?}", duration);

        // 验证性能要求（这里设置一个合理的阈值）
        assert!(duration.as_millis() < 1000, "指标记录性能不达标: {:?}", duration);
    }

    /// 基准测试 - 配置验证性能
    #[test]
    fn bench_config_validation() {
        let config = TunConfig::default();
        let start = Instant::now();

        // 多次验证配置
        for _ in 0..10000 {
            let manager = NetStackManager::new(config.clone());
            let _ = manager.validate_config();
        }

        let duration = start.elapsed();
        println!("验证 10,000 次配置耗时: {:?}", duration);

        // 验证性能要求
        assert!(duration.as_millis() < 100, "配置验证性能不达标: {:?}", duration);
    }
}
